<template>
  <div id="app">
    <!-- 手机模拟器外壳 -->
    <div class="phone-simulator">
      <div class="phone-frame">
        <div class="phone-screen">
          <div class="status-bar">
            <div class="status-left">
              <span class="time">{{ currentTime }}</span>
            </div>
            <div class="status-center">
              <div class="notch"></div>
            </div>
            <div class="status-right">
              <span class="signal">●●●●</span>
              <span class="wifi">📶</span>
              <span class="battery">🔋 85%</span>
            </div>
          </div>
          <div class="app-content">
            <div class="main-content">
              <router-view />
            </div>
            <!-- 底部导航栏 -->
            <van-tabbar v-model="activeTab" @change="onTabChange">
              <van-tabbar-item icon="home-o" name="home">首页</van-tabbar-item>
              <van-tabbar-item icon="exchange" name="transfer">转账</van-tabbar-item>
              <van-tabbar-item icon="gold-coin-o" name="token">Token</van-tabbar-item>
              <van-tabbar-item icon="user-o" name="profile">我的</van-tabbar-item>
            </van-tabbar>
            <!-- Home indicator -->
            <div class="home-indicator"></div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'App',
  setup() {
    const currentTime = ref('')
    const activeTab = ref('home')
    const router = useRouter()
    const route = useRoute()
    let timeInterval = null

    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    }

    const onTabChange = (name) => {
      // 根据tab切换路由
      const routes = {
        home: '/',
        transfer: '/transfer',
        token: '/token',
        profile: '/profile'
      }
      if (routes[name] && routes[name] !== route.path) {
        router.push(routes[name])
      }
    }

    // 监听路由变化，更新activeTab
    watch(() => route.path, (newPath) => {
      const pathToTab = {
        '/': 'home',
        '/transfer': 'transfer',
        '/token': 'token',
        '/profile': 'profile'
      }
      if (pathToTab[newPath]) {
        activeTab.value = pathToTab[newPath]
      }
    }, { immediate: true })

    onMounted(() => {
      // 设置页面标题
      document.title = '数字银行 - Digital Token演示'

      // 更新时间
      updateTime()
      timeInterval = setInterval(updateTime, 1000)

      // 禁用双击缩放
      document.addEventListener('touchstart', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault()
        }
      })

      let lastTouchEnd = 0
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime()
        if (now - lastTouchEnd <= 300) {
          event.preventDefault()
        }
        lastTouchEnd = now
      }, false)
    })

    onUnmounted(() => {
      if (timeInterval) {
        clearInterval(timeInterval)
      }
    })

    return {
      currentTime,
      activeTab,
      onTabChange
    }
  }
}
</script>

<style lang="scss">
#app {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 手机模拟器样式
.phone-simulator {
  display: flex;
  align-items: center;
  justify-content: center;

  .phone-frame {
    width: 375px;
    height: 812px;
    background: #000;
    border-radius: 40px;
    padding: 8px;
    box-shadow:
      0 0 0 2px #333,
      0 0 0 7px #000,
      0 30px 60px rgba(0, 0, 0, 0.4);
    position: relative;

    // 电源键
    &::before {
      content: '';
      position: absolute;
      right: -3px;
      top: 180px;
      width: 3px;
      height: 60px;
      background: #333;
      border-radius: 0 2px 2px 0;
    }

    // 音量键
    &::after {
      content: '';
      position: absolute;
      left: -3px;
      top: 160px;
      width: 3px;
      height: 40px;
      background: #333;
      border-radius: 2px 0 0 2px;
    }

    .phone-screen {
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 32px;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .status-bar {
      height: 44px;
      background: rgba(0, 0, 0, 0.02);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      font-size: 14px;
      font-weight: 600;
      color: #000;
      position: relative;
      z-index: 1000;

      .status-left {
        flex: 1;

        .time {
          font-weight: 600;
        }
      }

      .status-center {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);

        .notch {
          width: 150px;
          height: 30px;
          background: #000;
          border-radius: 0 0 20px 20px;
        }
      }

      .status-right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;
        font-size: 12px;

        .signal, .wifi, .battery {
          font-size: 12px;
        }
      }
    }

    .app-content {
      flex: 1;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .main-content {
      flex: 1;
      overflow-y: auto;
      position: relative;
    }

    .home-indicator {
      width: 134px;
      height: 5px;
      background: #000;
      border-radius: 3px;
      opacity: 0.3;
      margin: 8px auto;
      flex-shrink: 0;
    }
  }
}

// 全局样式覆盖
.van-nav-bar {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);

  .van-nav-bar__title {
    color: #fff;
    font-weight: 600;
  }

  .van-icon {
    color: #fff;
  }
}

.van-tabbar {
  background: #fff;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  position: relative !important; /* 覆盖fixed定位 */
  bottom: auto !important;
  left: auto !important;
  right: auto !important;
  width: 100% !important;
  z-index: 1 !important;
}

.van-button--primary {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  border: none;
}

.van-cell {
  padding: 16px;
}

// 响应式适配
@media screen and (max-width: 768px) {
  #app {
    padding: 10px;
  }

  .phone-simulator .phone-frame {
    width: 320px;
    height: 690px;
    border-radius: 30px;

    .phone-screen {
      border-radius: 24px;
    }
  }
}

@media screen and (max-width: 480px) {
  .phone-simulator .phone-frame {
    width: 280px;
    height: 600px;
  }
}
</style>

[{"D:\\VSCode\\DT_PRD1\\src\\main.js": "1", "D:\\VSCode\\DT_PRD1\\src\\permission.js": "2", "D:\\VSCode\\DT_PRD1\\src\\App.vue": "3", "D:\\VSCode\\DT_PRD1\\src\\router\\index.js": "4", "D:\\VSCode\\DT_PRD1\\src\\store\\index.js": "5", "D:\\VSCode\\DT_PRD1\\src\\utils\\auth.js": "6", "D:\\VSCode\\DT_PRD1\\src\\views\\404.vue": "7", "D:\\VSCode\\DT_PRD1\\src\\views\\Login.vue": "8", "D:\\VSCode\\DT_PRD1\\src\\views\\Dashboard.vue": "9", "D:\\VSCode\\DT_PRD1\\src\\views\\support\\TokenReset.vue": "10", "D:\\VSCode\\DT_PRD1\\src\\views\\support\\CustomerSupport.vue": "11", "D:\\VSCode\\DT_PRD1\\src\\views\\audit\\ComplianceCheck.vue": "12", "D:\\VSCode\\DT_PRD1\\src\\views\\audit\\AuditRules.vue": "13", "D:\\VSCode\\DT_PRD1\\src\\views\\audit\\AuditReports.vue": "14", "D:\\VSCode\\DT_PRD1\\src\\views\\business\\TokenManagement.vue": "15", "D:\\VSCode\\DT_PRD1\\src\\views\\business\\CustomerManagement.vue": "16", "D:\\VSCode\\DT_PRD1\\src\\layout\\index.vue": "17", "D:\\VSCode\\DT_PRD1\\src\\views\\business\\Statistics.vue": "18", "D:\\VSCode\\DT_PRD1\\src\\views\\security\\SecurityPolicy.vue": "19", "D:\\VSCode\\DT_PRD1\\src\\views\\security\\KeyManagement.vue": "20", "D:\\VSCode\\DT_PRD1\\src\\views\\security\\SecurityEvents.vue": "21", "D:\\VSCode\\DT_PRD1\\src\\views\\business\\BatchOperations.vue": "22", "D:\\VSCode\\DT_PRD1\\src\\views\\system\\SystemConfig.vue": "23", "D:\\VSCode\\DT_PRD1\\src\\views\\system\\SystemMonitor.vue": "24", "D:\\VSCode\\DT_PRD1\\src\\store\\modules\\app.js": "25", "D:\\VSCode\\DT_PRD1\\src\\store\\modules\\user.js": "26", "D:\\VSCode\\DT_PRD1\\src\\views\\system\\AdminManagement.vue": "27", "D:\\VSCode\\DT_PRD1\\src\\api\\user.js": "28", "D:\\VSCode\\DT_PRD1\\src\\utils\\request.js": "29", "D:\\VSCode\\DT_PRD1\\src\\utils\\validate.js": "30", "D:\\VSCode\\DT_PRD1\\src\\layout\\mixin\\ResizeHandler.js": "31", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\index.js": "32", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Navbar.vue": "33", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\AppMain.vue": "34", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\index.vue": "35", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\SidebarItem.vue": "36", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\Logo.vue": "37", "D:\\VSCode\\DT_PRD1\\src\\components\\Breadcrumb\\index.vue": "38", "D:\\VSCode\\DT_PRD1\\src\\components\\Screenfull\\index.vue": "39", "D:\\VSCode\\DT_PRD1\\src\\components\\SizeSelect\\index.vue": "40", "D:\\VSCode\\DT_PRD1\\src\\components\\Hamburger\\index.vue": "41", "D:\\VSCode\\DT_PRD1\\src\\components\\HeaderSearch\\index.vue": "42", "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\Link.vue": "43"}, {"size": 1115, "mtime": 1753024442758, "results": "44", "hashOfConfig": "45"}, {"size": 2599, "mtime": 1753024630339, "results": "46", "hashOfConfig": "45"}, {"size": 1018, "mtime": 1753024453406, "results": "47", "hashOfConfig": "45"}, {"size": 7298, "mtime": 1753024506591, "results": "48", "hashOfConfig": "45"}, {"size": 644, "mtime": 1753024528097, "results": "49", "hashOfConfig": "45"}, {"size": 1881, "mtime": 1753024566307, "results": "50", "hashOfConfig": "45"}, {"size": 1706, "mtime": 1753025069782, "results": "51", "hashOfConfig": "45"}, {"size": 7195, "mtime": 1753024664855, "results": "52", "hashOfConfig": "45"}, {"size": 7110, "mtime": 1753024983250, "results": "53", "hashOfConfig": "45"}, {"size": 5808, "mtime": 1753025950411, "results": "54", "hashOfConfig": "45"}, {"size": 3129, "mtime": 1753025923932, "results": "55", "hashOfConfig": "45"}, {"size": 2571, "mtime": 1753025850651, "results": "56", "hashOfConfig": "45"}, {"size": 3229, "mtime": 1753025818518, "results": "57", "hashOfConfig": "45"}, {"size": 4056, "mtime": 1753025836535, "results": "58", "hashOfConfig": "45"}, {"size": 3931, "mtime": 1753025871519, "results": "59", "hashOfConfig": "45"}, {"size": 12437, "mtime": 1753025168482, "results": "60", "hashOfConfig": "45"}, {"size": 1845, "mtime": 1753024715894, "results": "61", "hashOfConfig": "45"}, {"size": 3278, "mtime": 1753025905079, "results": "62", "hashOfConfig": "45"}, {"size": 9596, "mtime": 1753025661021, "results": "63", "hashOfConfig": "45"}, {"size": 5236, "mtime": 1753025687184, "results": "64", "hashOfConfig": "45"}, {"size": 2461, "mtime": 1753025802537, "results": "65", "hashOfConfig": "45"}, {"size": 2729, "mtime": 1753025887012, "results": "66", "hashOfConfig": "45"}, {"size": 5919, "mtime": 1753025581403, "results": "67", "hashOfConfig": "45"}, {"size": 8930, "mtime": 1753025621281, "results": "68", "hashOfConfig": "45"}, {"size": 1548, "mtime": 1753024552402, "results": "69", "hashOfConfig": "45"}, {"size": 2387, "mtime": 1753024542430, "results": "70", "hashOfConfig": "45"}, {"size": 13495, "mtime": 1753025223846, "results": "71", "hashOfConfig": "45"}, {"size": 4334, "mtime": 1753024611058, "results": "72", "hashOfConfig": "45"}, {"size": 2978, "mtime": 1753024582007, "results": "73", "hashOfConfig": "45"}, {"size": 2951, "mtime": 1753025038315, "results": "74", "hashOfConfig": "45"}, {"size": 1235, "mtime": 1753025055781, "results": "75", "hashOfConfig": "45"}, {"size": 139, "mtime": 1753024745551, "results": "76", "hashOfConfig": "45"}, {"size": 4380, "mtime": 1753025268863, "results": "77", "hashOfConfig": "45"}, {"size": 1143, "mtime": 1753024995796, "results": "78", "hashOfConfig": "45"}, {"size": 1793, "mtime": 1753024930398, "results": "79", "hashOfConfig": "45"}, {"size": 2662, "mtime": 1753025014505, "results": "80", "hashOfConfig": "45"}, {"size": 1711, "mtime": 1753024945797, "results": "81", "hashOfConfig": "45"}, {"size": 1681, "mtime": 1753024813645, "results": "82", "hashOfConfig": "45"}, {"size": 2416, "mtime": 1753024831512, "results": "83", "hashOfConfig": "45"}, {"size": 963, "mtime": 1753024841201, "results": "84", "hashOfConfig": "45"}, {"size": 736, "mtime": 1753024778786, "results": "85", "hashOfConfig": "45"}, {"size": 3379, "mtime": 1753024910865, "results": "86", "hashOfConfig": "45"}, {"size": 562, "mtime": 1753025024026, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "16c3dqz", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\VSCode\\DT_PRD1\\src\\main.js", ["217"], [], "D:\\VSCode\\DT_PRD1\\src\\permission.js", ["218"], [], "D:\\VSCode\\DT_PRD1\\src\\App.vue", ["219"], [], "D:\\VSCode\\DT_PRD1\\src\\router\\index.js", ["220"], [], "D:\\VSCode\\DT_PRD1\\src\\store\\index.js", ["221"], [], "D:\\VSCode\\DT_PRD1\\src\\utils\\auth.js", ["222"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\404.vue", ["223"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\Login.vue", ["224"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\Dashboard.vue", ["225"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\support\\TokenReset.vue", ["226"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\support\\CustomerSupport.vue", ["227"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\audit\\ComplianceCheck.vue", ["228"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\audit\\AuditRules.vue", ["229"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\audit\\AuditReports.vue", ["230"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\business\\TokenManagement.vue", ["231"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\business\\CustomerManagement.vue", ["232"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\index.vue", ["233"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\business\\Statistics.vue", ["234"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\security\\SecurityPolicy.vue", ["235"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\security\\KeyManagement.vue", ["236"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\security\\SecurityEvents.vue", ["237"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\business\\BatchOperations.vue", ["238"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\system\\SystemConfig.vue", ["239"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\system\\SystemMonitor.vue", ["240"], [], "D:\\VSCode\\DT_PRD1\\src\\store\\modules\\app.js", ["241"], [], "D:\\VSCode\\DT_PRD1\\src\\store\\modules\\user.js", ["242"], [], "D:\\VSCode\\DT_PRD1\\src\\views\\system\\AdminManagement.vue", ["243"], [], "D:\\VSCode\\DT_PRD1\\src\\api\\user.js", ["244"], [], "D:\\VSCode\\DT_PRD1\\src\\utils\\request.js", ["245"], [], "D:\\VSCode\\DT_PRD1\\src\\utils\\validate.js", ["246"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\mixin\\ResizeHandler.js", ["247"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\index.js", ["248"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Navbar.vue", ["249"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\AppMain.vue", ["250"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\index.vue", ["251"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\SidebarItem.vue", ["252"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\Logo.vue", ["253"], [], "D:\\VSCode\\DT_PRD1\\src\\components\\Breadcrumb\\index.vue", ["254"], [], "D:\\VSCode\\DT_PRD1\\src\\components\\Screenfull\\index.vue", ["255"], [], "D:\\VSCode\\DT_PRD1\\src\\components\\SizeSelect\\index.vue", ["256"], [], "D:\\VSCode\\DT_PRD1\\src\\components\\Hamburger\\index.vue", ["257"], [], "D:\\VSCode\\DT_PRD1\\src\\components\\HeaderSearch\\index.vue", ["258"], [], "D:\\VSCode\\DT_PRD1\\src\\layout\\components\\Sidebar\\Link.vue", ["259"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, {"ruleId": null, "fatal": true, "severity": 2, "message": "260", "nodeType": null}, "Parsing error: Cannot find module '@babel/eslint-parser'\nRequire stack:\n- D:\\VSCode\\DT_PRD1\\node_modules\\vue-eslint-parser\\index.js\n- D:\\VSCode\\DT_PRD1\\node_modules\\eslint-plugin-vue\\lib\\configs\\flat\\base.js\n- D:\\VSCode\\DT_PRD1\\node_modules\\eslint-plugin-vue\\lib\\index.js\n- D:\\VSCode\\DT_PRD1\\node_modules\\@eslint\\eslintrc\\dist\\eslintrc.cjs"]
<template>
  <div class="token-container">
    <!-- 导航栏 -->
    <van-nav-bar title="Digital Token" fixed>
      <template #right>
        <van-icon name="plus" size="20" @click="goToTokenBind" />
      </template>
    </van-nav-bar>

    <div class="page-content">
      <!-- Token状态概览 -->
      <div class="token-overview">
        <div class="overview-header">
          <h3>Token状态</h3>
          <van-tag :type="hasActiveToken ? 'success' : 'warning'" size="medium">
            {{ hasActiveToken ? '已激活' : '未绑定' }}
          </van-tag>
        </div>
        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-number">{{ tokens.length }}</div>
            <div class="stat-label">已绑定</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ activeTokenCount }}</div>
            <div class="stat-label">活跃中</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ todayAuthCount }}</div>
            <div class="stat-label">今日认证</div>
          </div>
        </div>
      </div>

      <!-- Token列表 -->
      <div class="token-list" v-if="tokens.length > 0">
        <div class="section-header">
          <h3>我的Token</h3>
        </div>
        <div
          v-for="token in tokens"
          :key="token.id"
          class="token-item"
          @click="selectToken(token)"
        >
          <div class="token-icon">
            <van-icon name="shield-o" size="24" :color="getTokenColor(token.status)" />
          </div>
          <div class="token-info">
            <div class="token-name">{{ token.name }}</div>
            <div class="token-id">ID: {{ token.id }}</div>
            <div class="token-time">绑定时间: {{ token.bindTime }}</div>
          </div>
          <div class="token-status">
            <van-tag :type="getStatusType(token.status)" size="small">
              {{ getStatusText(token.status) }}
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <van-empty
          image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
          description="还没有绑定Token"
        >
          <van-button type="primary" @click="goToTokenBind">
            立即绑定
          </van-button>
        </van-empty>
      </div>

      <!-- 安全提示 -->
      <div class="security-tips">
        <div class="section-header">
          <h3>安全提示</h3>
        </div>
        <div class="tips-content">
          <div class="tip-item">
            <van-icon name="info-o" size="16" color="#1989fa" />
            <span>Digital Token是您账户的重要安全保障</span>
          </div>
          <div class="tip-item">
            <van-icon name="info-o" size="16" color="#1989fa" />
            <span>请妥善保管您的Token设备，避免丢失</span>
          </div>
          <div class="tip-item">
            <van-icon name="info-o" size="16" color="#1989fa" />
            <span>如发现异常情况，请立即联系客服</span>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-operations">
        <van-button
          block
          type="primary"
          size="large"
          @click="goToTokenBind"
          v-if="!hasActiveToken"
        >
          绑定新Token
        </van-button>
        <div v-else class="operation-buttons">
          <van-button type="primary" @click="testTokenAuth">
            测试认证
          </van-button>
          <van-button type="default" @click="goToTokenBind">
            绑定新Token
          </van-button>
        </div>
      </div>
    </div>



    <!-- Token详情弹窗 -->
    <van-popup
      v-model:show="showTokenDetail"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="token-detail-popup" v-if="selectedToken">
        <div class="detail-header">
          <h3>Token详情</h3>
          <van-icon name="cross" @click="showTokenDetail = false" />
        </div>
        <div class="detail-content">
          <div class="detail-item">
            <span class="label">Token名称</span>
            <span class="value">{{ selectedToken.name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Token ID</span>
            <span class="value">{{ selectedToken.id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">设备类型</span>
            <span class="value">{{ getTokenTypeText(selectedToken.type) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">状态</span>
            <van-tag :type="getStatusType(selectedToken.status)">
              {{ getStatusText(selectedToken.status) }}
            </van-tag>
          </div>
          <div class="detail-item">
            <span class="label">绑定时间</span>
            <span class="value">{{ selectedToken.bindTime }}</span>
          </div>
          <div class="detail-item">
            <span class="label">最后使用</span>
            <span class="value">{{ selectedToken.lastUsed || '未使用' }}</span>
          </div>
        </div>
        <div class="detail-actions">
          <van-button
            type="primary"
            block
            @click="handleTokenAction"
            v-if="selectedToken.status === 'active'"
          >
            测试认证
          </van-button>
          <van-button
            type="warning"
            block
            @click="handleFreezeToken"
            v-if="selectedToken.status === 'active'"
          >
            冻结Token
          </van-button>
          <van-button
            type="success"
            block
            @click="handleUnfreezeToken"
            v-if="selectedToken.status === 'frozen'"
          >
            解冻Token
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showToast, showConfirmDialog } from 'vant'

export default {
  name: 'Token',
  setup() {
    const router = useRouter()
    const store = useStore()

    const showTokenDetail = ref(false)
    const selectedToken = ref(null)

    const tokens = computed(() => store.getters.tokens)
    const hasActiveToken = computed(() => store.getters.hasActiveToken)
    
    const activeTokenCount = computed(() => {
      return tokens.value.filter(token => token.status === 'active').length
    })
    
    const todayAuthCount = ref(12) // 模拟今日认证次数

    onMounted(() => {
      store.dispatch('fetchTokens')
    })

    const getTokenColor = (status) => {
      const colorMap = {
        active: '#07c160',
        frozen: '#ee0a24',
        expired: '#ff976a'
      }
      return colorMap[status] || '#969799'
    }

    const getStatusType = (status) => {
      const typeMap = {
        active: 'success',
        frozen: 'danger',
        expired: 'warning'
      }
      return typeMap[status] || 'default'
    }

    const getStatusText = (status) => {
      const textMap = {
        active: '正常',
        frozen: '冻结',
        expired: '过期'
      }
      return textMap[status] || '未知'
    }

    const getTokenTypeText = (type) => {
      const typeMap = {
        hardware: '硬件令牌',
        software: '软件令牌',
        sms: '短信令牌'
      }
      return typeMap[type] || '硬件令牌'
    }

    const selectToken = (token) => {
      selectedToken.value = token
      showTokenDetail.value = true
    }

    const goToTokenBind = () => {
      router.push('/token-bind')
    }

    const testTokenAuth = () => {
      if (!hasActiveToken.value) {
        showToast('请先绑定Token')
        return
      }
      router.push('/token-auth')
    }

    const handleTokenAction = () => {
      router.push('/token-auth')
      showTokenDetail.value = false
    }

    const handleFreezeToken = async () => {
      try {
        await showConfirmDialog({
          title: '确认冻结',
          message: '冻结后Token将无法使用，确定要冻结吗？'
        })
        
        store.commit('UPDATE_TOKEN_STATUS', {
          tokenId: selectedToken.value.id,
          status: 'frozen'
        })
        
        showToast('Token已冻结')
        showTokenDetail.value = false
      } catch (error) {
        // 用户取消
      }
    }

    const handleUnfreezeToken = async () => {
      try {
        await showConfirmDialog({
          title: '确认解冻',
          message: '解冻后Token将恢复正常使用，确定要解冻吗？'
        })
        
        store.commit('UPDATE_TOKEN_STATUS', {
          tokenId: selectedToken.value.id,
          status: 'active'
        })
        
        showToast('Token已解冻')
        showTokenDetail.value = false
      } catch (error) {
        // 用户取消
      }
    }

    return {
      tokens,
      hasActiveToken,
      activeTokenCount,
      todayAuthCount,
      showTokenDetail,
      selectedToken,
      getTokenColor,
      getStatusType,
      getStatusText,
      getTokenTypeText,
      selectToken,
      goToTokenBind,
      testTokenAuth,
      handleTokenAction,
      handleFreezeToken,
      handleUnfreezeToken
    }
  }
}
</script>

<style lang="scss" scoped>
.token-container {
  height: 100%;
  background-color: #f7f8fa;
  padding-top: 46px;
  padding-bottom: 50px;
  overflow-y: auto;
}

.page-content {
  padding: 16px;
}

.token-overview {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  border-radius: 12px;
  padding: 20px;
  color: #fff;
  margin-bottom: 16px;

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
    }
  }

  .overview-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }
}

.token-list,
.security-tips {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;

  .section-header {
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.token-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f7f8fa;
  cursor: pointer;

  &:last-child {
    border-bottom: none;
  }

  .token-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
  }

  .token-info {
    flex: 1;

    .token-name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .token-id {
      font-size: 12px;
      color: #969799;
      margin-bottom: 2px;
    }

    .token-time {
      font-size: 12px;
      color: #969799;
    }
  }

  .token-status {
    margin-left: 12px;
  }
}

.empty-state {
  background: #fff;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  margin-bottom: 16px;
}

.tips-content {
  .tip-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    color: #646566;

    &:last-child {
      margin-bottom: 0;
    }

    .van-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }
  }
}

.quick-operations {
  .operation-buttons {
    display: flex;
    gap: 12px;

    .van-button {
      flex: 1;
    }
  }
}

.token-detail-popup {
  padding: 20px;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebedf0;

    h3 {
      font-size: 18px;
      font-weight: 600;
    }

    .van-icon {
      cursor: pointer;
    }
  }

  .detail-content {
    margin-bottom: 20px;

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f7f8fa;

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-size: 14px;
        color: #646566;
      }

      .value {
        font-size: 14px;
        color: #323233;
        font-weight: 500;
      }
    }
  }

  .detail-actions {
    .van-button {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>

<template>
  <div class="home-container">
    <!-- 导航栏 -->
    <van-nav-bar :title="greeting" fixed>
      <template #right>
        <van-icon name="bell-o" size="20" @click="showNotifications" />
      </template>
    </van-nav-bar>

    <div class="page-content">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="user-info">
          <div class="avatar">
            <van-icon name="user-circle-o" size="50" color="#1989fa" />
          </div>
          <div class="user-details">
            <h3>{{ user.name }}</h3>
            <p class="user-type">{{ getUserTypeText(user.type) }}</p>
          </div>
          <div class="user-actions">
            <van-tag :type="getTokenStatusType()" size="medium">
              {{ getTokenStatusText() }}
            </van-tag>
          </div>
        </div>
        <div class="balance-info">
          <div class="balance-label">账户余额（元）</div>
          <div class="balance-amount">{{ formatAmount(user.balance) }}</div>
        </div>
      </div>

      <!-- 快捷功能 -->
      <div class="quick-actions">
        <div class="action-grid">
          <div class="action-item" @click="goToTransfer">
            <div class="action-icon">
              <van-icon name="exchange" size="24" color="#1989fa" />
            </div>
            <span>转账</span>
          </div>
          <div class="action-item" @click="goToToken">
            <div class="action-icon">
              <van-icon name="shield-o" size="24" color="#07c160" />
            </div>
            <span>Digital Token</span>
          </div>
          <div class="action-item" @click="showComingSoon">
            <div class="action-icon">
              <van-icon name="credit-pay" size="24" color="#ff976a" />
            </div>
            <span>理财</span>
          </div>
          <div class="action-item" @click="showComingSoon">
            <div class="action-icon">
              <van-icon name="records" size="24" color="#ee0a24" />
            </div>
            <span>明细</span>
          </div>
        </div>
      </div>

      <!-- 安全提醒 -->
      <div class="security-notice" v-if="!hasActiveToken">
        <div class="notice-header">
          <van-icon name="warning-o" size="20" color="#ff976a" />
          <span>安全提醒</span>
        </div>
        <p class="notice-content">
          您还未绑定Digital Token，建议立即绑定以提升账户安全性
        </p>
        <van-button size="small" type="primary" @click="goToTokenBind">
          立即绑定
        </van-button>
      </div>

      <!-- 最近交易 -->
      <div class="recent-transactions">
        <div class="section-header">
          <h3>最近交易</h3>
          <span class="more-link" @click="showComingSoon">查看全部</span>
        </div>
        <div class="transaction-list">
          <div
            v-for="transaction in recentTransactions"
            :key="transaction.id"
            class="transaction-item"
          >
            <div class="transaction-icon">
              <van-icon
                :name="transaction.type === 'in' ? 'arrow-down' : 'arrow-up'"
                :color="transaction.type === 'in' ? '#07c160' : '#ee0a24'"
                size="20"
              />
            </div>
            <div class="transaction-info">
              <div class="transaction-title">{{ transaction.title }}</div>
              <div class="transaction-time">{{ transaction.time }}</div>
            </div>
            <div class="transaction-amount" :class="transaction.type">
              {{ transaction.type === 'in' ? '+' : '-' }}{{ formatAmount(transaction.amount) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 安全中心 -->
      <div class="security-center">
        <div class="section-header">
          <h3>安全中心</h3>
        </div>
        <div class="security-items">
          <div class="security-item" @click="goToToken">
            <div class="security-icon">
              <van-icon name="shield-o" size="20" color="#1989fa" />
            </div>
            <div class="security-content">
              <div class="security-title">Digital Token</div>
              <div class="security-desc">硬件令牌安全认证</div>
            </div>
            <van-icon name="arrow" color="#c8c9cc" />
          </div>
          <div class="security-item" @click="showComingSoon">
            <div class="security-icon">
              <van-icon name="lock" size="20" color="#07c160" />
            </div>
            <div class="security-content">
              <div class="security-title">密码管理</div>
              <div class="security-desc">修改登录密码</div>
            </div>
            <van-icon name="arrow" color="#c8c9cc" />
          </div>
          <div class="security-item" @click="showComingSoon">
            <div class="security-icon">
              <van-icon name="finger-print" size="20" color="#ff976a" />
            </div>
            <div class="security-content">
              <div class="security-title">生物识别</div>
              <div class="security-desc">指纹/面容识别设置</div>
            </div>
            <van-icon name="arrow" color="#c8c9cc" />
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showToast } from 'vant'

export default {
  name: 'Home',
  setup() {
    const router = useRouter()
    const store = useStore()


    const user = computed(() => store.getters.user)
    const hasActiveToken = computed(() => store.getters.hasActiveToken)

    const greeting = computed(() => {
      const hour = new Date().getHours()
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    })

    const recentTransactions = ref([
      {
        id: 1,
        type: 'out',
        title: '转账给李四',
        time: '今天 14:30',
        amount: 5000
      },
      {
        id: 2,
        type: 'in',
        title: '工资到账',
        time: '昨天 09:15',
        amount: 12000
      },
      {
        id: 3,
        type: 'out',
        title: '信用卡还款',
        time: '01-18 16:20',
        amount: 3500
      }
    ])

    onMounted(() => {
      // 获取用户令牌列表
      store.dispatch('fetchTokens')
    })

    const getUserTypeText = (type) => {
      const typeMap = {
        personal: '个人客户',
        vip: 'VIP客户',
        enterprise: '企业客户'
      }
      return typeMap[type] || '个人客户'
    }

    const getTokenStatusType = () => {
      return hasActiveToken.value ? 'success' : 'warning'
    }

    const getTokenStatusText = () => {
      return hasActiveToken.value ? 'Token已绑定' : 'Token未绑定'
    }

    const formatAmount = (amount) => {
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    }

    const goToTransfer = () => {
      router.push('/transfer')
    }

    const goToToken = () => {
      router.push('/token')
    }

    const goToTokenBind = () => {
      router.push('/token-bind')
    }

    const showNotifications = () => {
      showToast('暂无新通知')
    }

    const showComingSoon = () => {
      showToast('功能开发中，敬请期待')
    }

    return {
      user,
      hasActiveToken,
      greeting,
      recentTransactions,
      getUserTypeText,
      getTokenStatusType,
      getTokenStatusText,
      formatAmount,
      goToTransfer,
      goToToken,
      goToTokenBind,
      showNotifications,
      showComingSoon
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  height: 100%;
  background-color: #f7f8fa;
  padding-top: 46px;
  padding-bottom: 50px;
  overflow-y: auto;
}

.page-content {
  padding: 16px;
}

.user-card {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  border-radius: 12px;
  padding: 20px;
  color: #fff;
  margin-bottom: 16px;

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .avatar {
      margin-right: 12px;
    }

    .user-details {
      flex: 1;

      h3 {
        font-size: 18px;
        margin-bottom: 4px;
      }

      .user-type {
        font-size: 14px;
        opacity: 0.8;
      }
    }
  }

  .balance-info {
    .balance-label {
      font-size: 14px;
      opacity: 0.8;
      margin-bottom: 8px;
    }

    .balance-amount {
      font-size: 28px;
      font-weight: 600;
      font-family: 'Helvetica Neue', Arial, sans-serif;
    }
  }
}

.quick-actions {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;

  .action-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #f7f8fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
        color: #323233;
      }
    }
  }
}

.security-notice {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #ff976a;

  .notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: #ff976a;

    .van-icon {
      margin-right: 8px;
    }
  }

  .notice-content {
    color: #646566;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.5;
  }
}

.recent-transactions,
.security-center {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 600;
    }

    .more-link {
      font-size: 14px;
      color: #1989fa;
    }
  }
}

.transaction-list {
  .transaction-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f7f8fa;

    &:last-child {
      border-bottom: none;
    }

    .transaction-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f7f8fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
    }

    .transaction-info {
      flex: 1;

      .transaction-title {
        font-size: 16px;
        color: #323233;
        margin-bottom: 4px;
      }

      .transaction-time {
        font-size: 12px;
        color: #969799;
      }
    }

    .transaction-amount {
      font-size: 16px;
      font-weight: 600;

      &.in {
        color: #07c160;
      }

      &.out {
        color: #ee0a24;
      }
    }
  }
}

.security-items {
  .security-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f7f8fa;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    .security-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f7f8fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
    }

    .security-content {
      flex: 1;

      .security-title {
        font-size: 16px;
        color: #323233;
        margin-bottom: 4px;
      }

      .security-desc {
        font-size: 12px;
        color: #969799;
      }
    }
  }
}
</style>

{"name": "eslint-plugin-n", "version": "15.7.0", "description": "Additional ESLint's rules for Node.js", "engines": {"node": ">=12.22.0"}, "main": "lib/index.js", "files": ["lib"], "peerDependencies": {"eslint": ">=7.0.0"}, "dependencies": {"builtins": "^5.0.1", "eslint-plugin-es": "^4.1.0", "eslint-utils": "^3.0.0", "ignore": "^5.1.1", "is-core-module": "^2.11.0", "minimatch": "^3.1.2", "resolve": "^1.22.1", "semver": "^7.3.8"}, "devDependencies": {"@typescript-eslint/parser": "^5.51.0", "codecov": "^3.3.0", "esbuild": "^0.14.39", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-doc-generator": "^1.4.2", "eslint-plugin-eslint-plugin": "^5.0.6", "eslint-plugin-n": "file:.", "fast-glob": "^3.2.12", "globals": "^13.17.0", "husky": "^8.0.2", "import-meta-resolve": "^1.1.1", "lint-staged": "^12.4.1", "markdownlint-cli": "^0.32.2", "mocha": "^10.1.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.1", "prettier": "^2.7.1", "punycode": "^2.1.1", "release-it": "^15.5.0", "rimraf": "^3.0.2", "typescript": "^4.9.5"}, "scripts": {"build": "node scripts/update", "clean": "rimraf .nyc_output coverage", "codecov": "nyc report --reporter text-lcov | codecov --pipe --disable=gcov -t $CODECOV_TOKEN", "coverage": "opener ./coverage/lcov-report/index.html", "format": "prettier --loglevel warn --write \"**/*.{js,css,md}\"", "lint": "npm-run-all \"lint:*\"", "lint:docs": "markdownlint \"**/*.md\"", "lint:eslint-docs": "npm run update:eslint-docs -- --check", "lint:js": "eslint lib scripts tests/lib .eslintrc.js", "new": "node scripts/new-rule", "postversion": "git push && git push --tags", "prepare": "npx husky install", "pretest": "npm run -s lint", "preversion": "npm test", "test": "nyc npm run -s test:_mocha", "test:_mocha": "_mocha \"tests/lib/**/*.js\" --reporter progress --timeout 4000", "test:ci": "nyc npm run -s test:_mocha", "update:eslint-docs": "eslint-doc-generator --config-emoji recommended-module,☑️ --config-emoji recommended-script,✔️ --rule-list-split meta.docs.category --ignore-config recommended-module --ignore-config recommended-script --url-configs \"https://github.com/eslint-community/eslint-plugin-n#-configs\"", "version": "npm run -s build && eslint lib/rules --fix && git add .", "watch": "npm run test:_mocha -- --watch --growl"}, "repository": {"type": "git", "url": "git+https://github.com/eslint-community/eslint-plugin-n.git"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "node", "nodejs", "ecmascript", "shebang", "file", "path", "import", "require"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/eslint-community/eslint-plugin-n/issues"}, "homepage": "https://github.com/eslint-community/eslint-plugin-n#readme", "funding": "https://github.com/sponsors/mysticatea", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "release-it": {"github": {"release": true}, "npm": {"skipChecks": true}}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{json,js}": "prettier --write --ignore-path .es<PERSON><PERSON><PERSON>"}}
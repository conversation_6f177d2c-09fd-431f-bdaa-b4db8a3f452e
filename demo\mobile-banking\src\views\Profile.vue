<template>
  <div class="profile-container">
    <!-- 导航栏 -->
    <van-nav-bar title="我的" fixed />

    <div class="page-content">
      <!-- 用户信息 -->
      <div class="user-profile">
        <div class="profile-header">
          <div class="avatar">
            <van-icon name="user-circle-o" size="60" color="#1989fa" />
          </div>
          <div class="user-info">
            <h3>{{ user.name }}</h3>
            <p>{{ getUserTypeText(user.type) }}</p>
            <van-tag type="success" size="small">已认证</van-tag>
          </div>
        </div>
        <div class="profile-stats">
          <div class="stat-item">
            <div class="stat-number">{{ tokens.length }}</div>
            <div class="stat-label">Token数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ todayTransactions }}</div>
            <div class="stat-label">今日交易</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ securityLevel }}</div>
            <div class="stat-label">安全等级</div>
          </div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="menu-section">
        <van-cell-group>
          <van-cell
            title="账户管理"
            is-link
            @click="showComingSoon"
          >
            <template #icon>
              <van-icon name="user-o" size="20" color="#1989fa" />
            </template>
          </van-cell>
          <van-cell
            title="Digital Token"
            is-link
            @click="goToToken"
          >
            <template #icon>
              <van-icon name="shield-o" size="20" color="#07c160" />
            </template>
            <template #value>
              <van-tag :type="hasActiveToken ? 'success' : 'warning'" size="small">
                {{ hasActiveToken ? '已绑定' : '未绑定' }}
              </van-tag>
            </template>
          </van-cell>
          <van-cell
            title="安全设置"
            is-link
            @click="showComingSoon"
          >
            <template #icon>
              <van-icon name="lock" size="20" color="#ff976a" />
            </template>
          </van-cell>
          <van-cell
            title="交易记录"
            is-link
            @click="showComingSoon"
          >
            <template #icon>
              <van-icon name="records" size="20" color="#ee0a24" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 服务菜单 -->
      <div class="menu-section">
        <van-cell-group>
          <van-cell
            title="客服中心"
            is-link
            @click="showComingSoon"
          >
            <template #icon>
              <van-icon name="service" size="20" color="#1989fa" />
            </template>
          </van-cell>
          <van-cell
            title="意见反馈"
            is-link
            @click="showComingSoon"
          >
            <template #icon>
              <van-icon name="comment-o" size="20" color="#07c160" />
            </template>
          </van-cell>
          <van-cell
            title="关于我们"
            is-link
            @click="showAbout"
          >
            <template #icon>
              <van-icon name="info-o" size="20" color="#ff976a" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 退出登录 -->
      <div class="logout-section">
        <van-button
          type="danger"
          block
          @click="handleLogout"
        >
          退出登录
        </van-button>
      </div>

      <!-- 版本信息 -->
      <div class="version-info">
        <p>数字银行 v1.0.0</p>
        <p>© 2024 Digital Token演示</p>
      </div>
    </div>



    <!-- 关于我们弹窗 -->
    <van-popup
      v-model:show="showAboutDialog"
      position="center"
      :style="{ width: '80%', maxWidth: '300px' }"
      round
    >
      <div class="about-popup">
        <div class="about-header">
          <van-icon name="shield-o" size="40" color="#1989fa" />
          <h3>数字银行</h3>
          <p>Digital Token演示版</p>
        </div>
        <div class="about-content">
          <p>这是一个Digital Token管理平台的手机银行端演示Demo，用于展示数字令牌在真实银行场景中的应用。</p>
          <div class="about-features">
            <div class="feature-item">
              <van-icon name="shield-o" size="16" color="#1989fa" />
              <span>安全认证</span>
            </div>
            <div class="feature-item">
              <van-icon name="exchange" size="16" color="#07c160" />
              <span>便捷转账</span>
            </div>
            <div class="feature-item">
              <van-icon name="eye-o" size="16" color="#ff976a" />
              <span>实时监控</span>
            </div>
          </div>
        </div>
        <div class="about-actions">
          <van-button type="primary" block @click="showAboutDialog = false">
            知道了
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showToast, showConfirmDialog } from 'vant'

export default {
  name: 'Profile',
  setup() {
    const router = useRouter()
    const store = useStore()

    const showAboutDialog = ref(false)

    const user = computed(() => store.getters.user)
    const tokens = computed(() => store.getters.tokens)
    const hasActiveToken = computed(() => store.getters.hasActiveToken)
    
    const todayTransactions = ref(8)
    const securityLevel = ref('高')

    const getUserTypeText = (type) => {
      const typeMap = {
        personal: '个人客户',
        vip: 'VIP客户',
        enterprise: '企业客户'
      }
      return typeMap[type] || '个人客户'
    }

    const goToToken = () => {
      router.push('/token')
    }

    const showAbout = () => {
      showAboutDialog.value = true
    }

    const showComingSoon = () => {
      showToast('功能开发中，敬请期待')
    }

    const handleLogout = async () => {
      try {
        await showConfirmDialog({
          title: '确认退出',
          message: '确定要退出登录吗？'
        })
        
        store.dispatch('logout')
        router.replace('/login')
        showToast('已退出登录')
      } catch (error) {
        // 用户取消
      }
    }

    return {
      showAboutDialog,
      user,
      tokens,
      hasActiveToken,
      todayTransactions,
      securityLevel,
      getUserTypeText,
      goToToken,
      showAbout,
      showComingSoon,
      handleLogout
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  height: 100%;
  background-color: #f7f8fa;
  padding-top: 46px;
  padding-bottom: 50px;
  overflow-y: auto;
}

.page-content {
  padding: 16px;
}

.user-profile {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  border-radius: 12px;
  padding: 24px;
  color: #fff;
  margin-bottom: 16px;

  .profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .avatar {
      margin-right: 16px;
    }

    .user-info {
      flex: 1;

      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      p {
        font-size: 14px;
        opacity: 0.8;
        margin-bottom: 8px;
      }
    }
  }

  .profile-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}

.menu-section {
  margin-bottom: 16px;

  .van-cell-group {
    border-radius: 12px;
    overflow: hidden;
  }

  .van-cell {
    padding: 16px;

    .van-icon {
      margin-right: 12px;
    }
  }
}

.logout-section {
  margin-bottom: 24px;
}

.version-info {
  text-align: center;
  color: #969799;
  font-size: 12px;

  p {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.about-popup {
  padding: 24px;
  text-align: center;

  .about-header {
    margin-bottom: 20px;

    .van-icon {
      margin-bottom: 12px;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #323233;
    }

    p {
      font-size: 14px;
      color: #646566;
    }
  }

  .about-content {
    margin-bottom: 24px;
    text-align: left;

    p {
      font-size: 14px;
      color: #646566;
      line-height: 1.6;
      margin-bottom: 16px;
    }

    .about-features {
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
        color: #323233;

        &:last-child {
          margin-bottom: 0;
        }

        .van-icon {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>

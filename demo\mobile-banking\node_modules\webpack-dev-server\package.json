{"name": "webpack-dev-server", "version": "4.15.2", "description": "Serves a webpack app. Updates the browser on changes.", "bin": "bin/webpack-dev-server.js", "main": "lib/Server.js", "types": "types/lib/Server.d.ts", "author": "<PERSON> @sokra", "bugs": "https://github.com/webpack/webpack-dev-server/issues", "homepage": "https://github.com/webpack/webpack-dev-server#readme", "repository": "https://github.com/webpack/webpack-dev-server", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "files": ["bin", "lib", "client", "types"], "engines": {"node": ">= 12.13.0"}, "scripts": {"fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint . --cache", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -p \"fmt:**\" \"lint:**\"", "fmt": "npm run fmt:check -- --write", "fix:js": "npm run lint:js -- --fix", "fix": "npm-run-all fix:js fmt", "commitlint": "commitlint --from=master", "build:client": "rimraf ./client/* && babel client-src/ --out-dir client/ --ignore \"client-src/webpack.config.js\" --ignore \"client-src/modules\" && webpack --config client-src/webpack.config.js", "build:types": "rimraf ./types/* && tsc --declaration --emitDeclarationOnly --outDir types && node ./scripts/extend-webpack-types.js && prettier \"types/**/*.ts\" --write && prettier \"types/**/*.ts\" --write", "build": "npm-run-all -p \"build:**\"", "test:only": "jest", "test:coverage": "npm run test:only -- --coverage", "test:watch": "npm run test:coverage --watch", "test": "npm run test:coverage", "pretest": "npm run lint", "prepare": "husky install && npm run build", "release": "standard-version"}, "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.5", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "launch-editor": "^2.6.0", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.4", "ws": "^8.13.0"}, "devDependencies": {"@babel/cli": "^7.17.3", "@babel/core": "^7.20.2", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-transform-object-assign": "^7.14.5", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.20.2", "@babel/runtime": "^7.20.1", "@commitlint/cli": "^16.2.3", "@commitlint/config-conventional": "^16.2.1", "@types/compression": "^1.7.2", "@types/default-gateway": "^3.0.1", "@types/node": "^18.11.9", "@types/node-forge": "^1.3.1", "@types/rimraf": "^3.0.2", "@types/sockjs-client": "^1.5.1", "@types/trusted-types": "^2.0.2", "acorn": "^8.2.4", "babel-jest": "^28.1.3", "babel-loader": "^8.2.4", "body-parser": "^1.19.2", "core-js": "^3.21.1", "css-loader": "^5.2.4", "eslint": "^8.28.0", "eslint-config-prettier": "^8.4.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.23.2", "execa": "^5.1.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^4.5.2", "http-proxy": "^1.18.1", "husky": "^7.0.0", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "klona": "^2.0.4", "less": "^4.1.1", "less-loader": "^7.3.0", "lint-staged": "^12.3.4", "marked": "^4.2.3", "memfs": "^3.4.12", "npm-run-all": "^4.1.5", "prettier": "^2.8.0", "puppeteer": "^13.4.1", "require-from-string": "^2.0.2", "rimraf": "^3.0.2", "sockjs-client": "^1.6.1", "standard-version": "^9.3.0", "strip-ansi-v6": "npm:strip-ansi@^6.0.0", "style-loader": "^2.0.0", "supertest": "^6.1.3", "tcp-port-used": "^1.0.2", "typescript": "^4.9.3", "url-loader": "^4.1.1", "wait-for-expect": "^3.0.2", "webpack": "^5.81.0", "webpack-cli": "^4.7.2", "webpack-merge": "^5.8.0"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack": {"optional": true}}}
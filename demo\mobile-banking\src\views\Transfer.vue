<template>
  <div class="transfer-container">
    <!-- 导航栏 -->
    <van-nav-bar title="转账" fixed />

    <div class="page-content">
      <!-- 转账表单 -->
      <div class="transfer-form">
        <van-form @submit="handleTransfer">
          <van-cell-group inset>
            <van-field
              v-model="form.toAccount"
              name="toAccount"
              label="收款账号"
              placeholder="请输入收款人账号"
              :rules="[{ required: true, message: '请输入收款账号' }]"
              clearable
            >
              <template #button>
                <van-button size="small" type="primary" @click="selectContact">
                  通讯录
                </van-button>
              </template>
            </van-field>
            
            <van-field
              v-model="form.toName"
              name="toName"
              label="收款人"
              placeholder="请输入收款人姓名"
              :rules="[{ required: true, message: '请输入收款人姓名' }]"
              clearable
            />
            
            <van-field
              v-model="form.amount"
              type="number"
              name="amount"
              label="转账金额"
              placeholder="请输入转账金额"
              :rules="[{ required: true, message: '请输入转账金额' }]"
              clearable
            >
              <template #button>
                <span class="currency">元</span>
              </template>
            </van-field>
            
            <van-field
              v-model="form.remark"
              name="remark"
              label="转账备注"
              placeholder="请输入转账备注（可选）"
              maxlength="50"
              show-word-limit
              clearable
            />
          </van-cell-group>

          <!-- 快捷金额 -->
          <div class="quick-amounts">
            <div class="amount-label">快捷金额</div>
            <div class="amount-buttons">
              <van-button
                v-for="amount in quickAmounts"
                :key="amount"
                size="small"
                type="default"
                @click="setAmount(amount)"
              >
                {{ amount }}
              </van-button>
            </div>
          </div>

          <!-- 转账按钮 -->
          <div class="transfer-button">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :disabled="!isFormValid"
            >
              下一步
            </van-button>
          </div>
        </van-form>
      </div>

      <!-- 最近转账 -->
      <div class="recent-transfers">
        <div class="section-header">
          <h3>最近转账</h3>
        </div>
        <div class="transfer-list">
          <div
            v-for="transfer in recentTransfers"
            :key="transfer.id"
            class="transfer-item"
            @click="selectRecentTransfer(transfer)"
          >
            <div class="transfer-avatar">
              <van-icon name="user-circle-o" size="40" color="#1989fa" />
            </div>
            <div class="transfer-info">
              <div class="transfer-name">{{ transfer.name }}</div>
              <div class="transfer-account">{{ transfer.account }}</div>
            </div>
            <div class="transfer-amount">
              ¥{{ formatAmount(transfer.amount) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 安全提示 -->
      <div class="security-tips">
        <div class="tips-header">
          <van-icon name="warning-o" size="16" color="#ff976a" />
          <span>安全提示</span>
        </div>
        <ul class="tips-list">
          <li>转账前请仔细核对收款人信息</li>
          <li>大额转账需要Digital Token认证</li>
          <li>如有疑问请及时联系客服</li>
        </ul>
      </div>
    </div>



    <!-- 联系人选择弹窗 -->
    <van-popup
      v-model:show="showContactList"
      position="bottom"
      :style="{ height: '60%' }"
      round
    >
      <div class="contact-popup">
        <div class="popup-header">
          <h3>选择联系人</h3>
          <van-icon name="cross" @click="showContactList = false" />
        </div>
        <div class="contact-list">
          <div
            v-for="contact in contacts"
            :key="contact.id"
            class="contact-item"
            @click="selectContactItem(contact)"
          >
            <div class="contact-avatar">
              <van-icon name="user-circle-o" size="40" color="#1989fa" />
            </div>
            <div class="contact-info">
              <div class="contact-name">{{ contact.name }}</div>
              <div class="contact-account">{{ contact.account }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showToast } from 'vant'

export default {
  name: 'Transfer',
  setup() {
    const router = useRouter()
    const store = useStore()

    const showContactList = ref(false)

    const form = reactive({
      toAccount: '',
      toName: '',
      amount: '',
      remark: ''
    })

    const quickAmounts = ['100', '500', '1000', '5000']
    
    const recentTransfers = ref([
      {
        id: 1,
        name: '李四',
        account: '6222***1234',
        amount: 5000
      },
      {
        id: 2,
        name: '王五',
        account: '6228***5678',
        amount: 2000
      },
      {
        id: 3,
        name: '赵六',
        account: '6225***9012',
        amount: 1500
      }
    ])

    const contacts = ref([
      {
        id: 1,
        name: '李四',
        account: '****************'
      },
      {
        id: 2,
        name: '王五',
        account: '****************'
      },
      {
        id: 3,
        name: '赵六',
        account: '****************'
      }
    ])

    const isFormValid = computed(() => {
      return form.toAccount && form.toName && form.amount && parseFloat(form.amount) > 0
    })

    const setAmount = (amount) => {
      form.amount = amount
    }

    const selectContact = () => {
      showContactList.value = true
    }

    const selectContactItem = (contact) => {
      form.toAccount = contact.account
      form.toName = contact.name
      showContactList.value = false
    }

    const selectRecentTransfer = (transfer) => {
      form.toAccount = transfer.account
      form.toName = transfer.name
      form.amount = transfer.amount.toString()
    }

    const formatAmount = (amount) => {
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    }

    const handleTransfer = () => {
      if (!isFormValid.value) {
        showToast('请填写完整信息')
        return
      }

      // 保存转账信息到store
      store.commit('SET_TRANSFER_INFO', {
        toAccount: form.toAccount,
        toName: form.toName,
        amount: parseFloat(form.amount),
        remark: form.remark
      })

      // 跳转到确认页面
      router.push('/transfer-confirm')
    }

    return {
      form,
      quickAmounts,
      recentTransfers,
      contacts,
      showContactList,
      isFormValid,
      setAmount,
      selectContact,
      selectContactItem,
      selectRecentTransfer,
      formatAmount,
      handleTransfer
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer-container {
  height: 100%;
  background-color: #f7f8fa;
  padding-top: 46px;
  padding-bottom: 50px;
  overflow-y: auto;
}

.page-content {
  padding: 16px;
}

.transfer-form {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;

  .currency {
    color: #646566;
    font-size: 14px;
  }
}

.quick-amounts {
  margin: 20px 0;

  .amount-label {
    font-size: 14px;
    color: #646566;
    margin-bottom: 12px;
  }

  .amount-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

.transfer-button {
  margin-top: 24px;
}

.recent-transfers,
.security-tips {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;

  .section-header {
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.transfer-list {
  .transfer-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f7f8fa;
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    .transfer-avatar {
      margin-right: 12px;
    }

    .transfer-info {
      flex: 1;

      .transfer-name {
        font-size: 16px;
        color: #323233;
        margin-bottom: 4px;
      }

      .transfer-account {
        font-size: 12px;
        color: #969799;
      }
    }

    .transfer-amount {
      font-size: 14px;
      color: #646566;
      font-weight: 500;
    }
  }
}

.security-tips {
  border-left: 4px solid #ff976a;

  .tips-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 600;
    color: #ff976a;

    .van-icon {
      margin-right: 8px;
    }
  }

  .tips-list {
    margin: 0;
    padding-left: 20px;

    li {
      font-size: 14px;
      color: #646566;
      line-height: 1.6;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.contact-popup {
  padding: 20px;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebedf0;

    h3 {
      font-size: 18px;
      font-weight: 600;
    }

    .van-icon {
      cursor: pointer;
    }
  }

  .contact-list {
    .contact-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f7f8fa;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      .contact-avatar {
        margin-right: 12px;
      }

      .contact-info {
        flex: 1;

        .contact-name {
          font-size: 16px;
          color: #323233;
          margin-bottom: 4px;
        }

        .contact-account {
          font-size: 12px;
          color: #969799;
        }
      }
    }
  }
}
</style>

/**
 * DON'T EDIT THIS FILE.
 * This file was generated by "scripts/update-lib-configs.js" script.
 */
"use strict"

module.exports = {
    plugins: ["es"],
    rules: {
        "es/no-array-from": "error",
        "es/no-array-of": "error",
        "es/no-arrow-functions": "error",
        "es/no-binary-numeric-literals": "error",
        "es/no-block-scoped-functions": "error",
        "es/no-block-scoped-variables": "error",
        "es/no-classes": "error",
        "es/no-computed-properties": "error",
        "es/no-default-parameters": "error",
        "es/no-destructuring": "error",
        "es/no-for-of-loops": "error",
        "es/no-generators": "error",
        "es/no-map": "error",
        "es/no-math-acosh": "error",
        "es/no-math-asinh": "error",
        "es/no-math-atanh": "error",
        "es/no-math-cbrt": "error",
        "es/no-math-clz32": "error",
        "es/no-math-cosh": "error",
        "es/no-math-expm1": "error",
        "es/no-math-fround": "error",
        "es/no-math-hypot": "error",
        "es/no-math-imul": "error",
        "es/no-math-log10": "error",
        "es/no-math-log1p": "error",
        "es/no-math-log2": "error",
        "es/no-math-sign": "error",
        "es/no-math-sinh": "error",
        "es/no-math-tanh": "error",
        "es/no-math-trunc": "error",
        "es/no-modules": "error",
        "es/no-new-target": "error",
        "es/no-number-epsilon": "error",
        "es/no-number-isfinite": "error",
        "es/no-number-isinteger": "error",
        "es/no-number-isnan": "error",
        "es/no-number-issafeinteger": "error",
        "es/no-number-maxsafeinteger": "error",
        "es/no-number-minsafeinteger": "error",
        "es/no-number-parsefloat": "error",
        "es/no-number-parseint": "error",
        "es/no-object-assign": "error",
        "es/no-object-getownpropertysymbols": "error",
        "es/no-object-is": "error",
        "es/no-object-setprototypeof": "error",
        "es/no-object-super-properties": "error",
        "es/no-octal-numeric-literals": "error",
        "es/no-promise": "error",
        "es/no-property-shorthands": "error",
        "es/no-proxy": "error",
        "es/no-reflect": "error",
        "es/no-regexp-u-flag": "error",
        "es/no-regexp-y-flag": "error",
        "es/no-rest-parameters": "error",
        "es/no-set": "error",
        "es/no-spread-elements": "error",
        "es/no-string-fromcodepoint": "error",
        "es/no-string-raw": "error",
        "es/no-subclassing-builtins": "error",
        "es/no-symbol": "error",
        "es/no-template-literals": "error",
        "es/no-typed-arrays": "error",
        "es/no-unicode-codepoint-escapes": "error",
        "es/no-weak-map": "error",
        "es/no-weak-set": "error",
    },
}
